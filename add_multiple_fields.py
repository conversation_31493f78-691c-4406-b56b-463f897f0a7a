#!/usr/bin/env python3
"""
直接添加多选字段到数据库
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db
from sqlalchemy import text

def add_multiple_fields():
    """添加多选支持字段"""
    with app.app_context():
        try:
            print('开始添加多选支持字段...')
            
            # 检查并添加 is_multiple 字段
            try:
                result = db.session.execute(text("""
                    SELECT COUNT(*) as count 
                    FROM information_schema.columns 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'attribute_groups' 
                    AND column_name = 'is_multiple'
                """)).fetchone()
                
                if result[0] == 0:
                    db.session.execute(text("""
                        ALTER TABLE attribute_groups 
                        ADD COLUMN is_multiple TINYINT(1) DEFAULT 0 NOT NULL
                    """))
                    print('✓ 添加 is_multiple 字段成功')
                else:
                    print('- is_multiple 字段已存在')
                    
            except Exception as e:
                print(f'添加 is_multiple 字段时出错: {e}')
            
            # 检查并添加 max_selections 字段
            try:
                result = db.session.execute(text("""
                    SELECT COUNT(*) as count 
                    FROM information_schema.columns 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'attribute_groups' 
                    AND column_name = 'max_selections'
                """)).fetchone()
                
                if result[0] == 0:
                    db.session.execute(text("""
                        ALTER TABLE attribute_groups 
                        ADD COLUMN max_selections INT DEFAULT NULL
                    """))
                    print('✓ 添加 max_selections 字段成功')
                else:
                    print('- max_selections 字段已存在')
                    
            except Exception as e:
                print(f'添加 max_selections 字段时出错: {e}')
            
            # 提交更改
            db.session.commit()
            print('✅ 多选支持字段添加完成！')
            
            # 验证字段是否添加成功
            result = db.session.execute(text("""
                SELECT column_name, column_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = 'attribute_groups' 
                AND column_name IN ('is_multiple', 'max_selections')
                ORDER BY column_name
            """)).fetchall()
            
            print('\n验证结果:')
            for row in result:
                print(f'  {row[0]}: {row[1]} (nullable: {row[2]}, default: {row[3]})')
            
        except Exception as e:
            db.session.rollback()
            print(f'❌ 操作失败: {e}')
            raise

if __name__ == '__main__':
    add_multiple_fields()
