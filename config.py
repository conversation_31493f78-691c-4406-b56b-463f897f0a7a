import os
from datetime import timedelta

class Config:
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-in-production'
    
    # 时区配置
    TIMEZONE = 'Asia/Shanghai'  # 中国时区
    
    # 数据库配置
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or 'localhost'
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or '123456'
    MYSQL_DB = os.environ.get('MYSQL_DB') or 'print_shop'
    
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}/{MYSQL_DB}?charset=utf8mb4'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'connect_args': {'charset': 'utf8mb4'}
    }
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False  # 生产环境设为True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = 'static/uploads'
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp', 'pdf', 'cdr', 'ai'}

    # 订单附件配置
    ATTACHMENT_EXTENSIONS = {'pdf', 'cdr', 'ai'}  # 订单附件允许的格式
    ATTACHMENT_MAX_SIZE = 50 * 1024 * 1024  # 50MB
    
    # 分页配置
    POSTS_PER_PAGE = 20
    
    # 邮件配置（可选）
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # 支付配置（预留）
    ALIPAY_APP_ID = os.environ.get('ALIPAY_APP_ID')
    ALIPAY_PRIVATE_KEY = os.environ.get('ALIPAY_PRIVATE_KEY')
    WECHAT_APP_ID = os.environ.get('WECHAT_APP_ID')
    WECHAT_MCH_ID = os.environ.get('WECHAT_MCH_ID')
    
    # 易支付配置
    EPAY_GATEWAY = os.environ.get('EPAY_GATEWAY') or 'https://zpayz.cn/'
    EPAY_PID = os.environ.get('EPAY_PID') or '2025053120440356'
    EPAY_KEY = os.environ.get('EPAY_KEY') or 'A35X9WDNHxCug8Mn6F6qvud60lsh6h0M'
    EPAY_SUBMIT_URL = os.environ.get('EPAY_SUBMIT_URL') or 'https://z-pay.cn/submit.php'
    EPAY_API_URL = os.environ.get('EPAY_API_URL') or 'https://z-pay.cn/api.php'

class DevelopmentConfig(Config):
    DEBUG = True
    
class ProductionConfig(Config):
    DEBUG = False
    SESSION_COOKIE_SECURE = True
    
class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
