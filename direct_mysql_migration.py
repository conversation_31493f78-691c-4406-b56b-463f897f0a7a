#!/usr/bin/env python3
"""
直接通过MySQL连接添加字段
"""

import pymysql
import os

def add_multiple_fields():
    """直接通过MySQL连接添加多选支持字段"""
    
    # 数据库连接配置
    config = {
        'host': os.environ.get('MYSQL_HOST', 'localhost'),
        'user': os.environ.get('MYSQL_USER', 'root'),
        'password': os.environ.get('MYSQL_PASSWORD', '123456'),
        'database': os.environ.get('MYSQL_DB', 'print_shop'),
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接数据库
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print('已连接到数据库')
        
        # 检查 is_multiple 字段是否存在
        cursor.execute("""
            SELECT COUNT(*) as count 
            FROM information_schema.columns 
            WHERE table_schema = %s 
            AND table_name = 'attribute_groups' 
            AND column_name = 'is_multiple'
        """, (config['database'],))
        
        result = cursor.fetchone()
        if result[0] == 0:
            cursor.execute("""
                ALTER TABLE attribute_groups 
                ADD COLUMN is_multiple TINYINT(1) DEFAULT 0 NOT NULL
            """)
            print('✓ 添加 is_multiple 字段成功')
        else:
            print('- is_multiple 字段已存在')
        
        # 检查 max_selections 字段是否存在
        cursor.execute("""
            SELECT COUNT(*) as count 
            FROM information_schema.columns 
            WHERE table_schema = %s 
            AND table_name = 'attribute_groups' 
            AND column_name = 'max_selections'
        """, (config['database'],))
        
        result = cursor.fetchone()
        if result[0] == 0:
            cursor.execute("""
                ALTER TABLE attribute_groups 
                ADD COLUMN max_selections INT DEFAULT NULL
            """)
            print('✓ 添加 max_selections 字段成功')
        else:
            print('- max_selections 字段已存在')
        
        # 提交更改
        connection.commit()
        print('✅ 多选支持字段添加完成！')
        
        # 验证字段是否添加成功
        cursor.execute("""
            SELECT column_name, column_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_schema = %s 
            AND table_name = 'attribute_groups' 
            AND column_name IN ('is_multiple', 'max_selections')
            ORDER BY column_name
        """, (config['database'],))
        
        results = cursor.fetchall()
        print('\n验证结果:')
        for row in results:
            print(f'  {row[0]}: {row[1]} (nullable: {row[2]}, default: {row[3]})')
        
    except Exception as e:
        print(f'❌ 操作失败: {e}')
        if 'connection' in locals():
            connection.rollback()
        raise
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()
        print('数据库连接已关闭')

if __name__ == '__main__':
    add_multiple_fields()
