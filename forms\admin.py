from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import StringField, TextAreaField, SelectField, SelectMultipleField, IntegerField, DecimalField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, Optional, ValidationError
from models.product import Category, AttributeGroup, Product, Attribute

class CategoryForm(FlaskForm):
    name = StringField('分类名称', validators=[
        DataRequired(message='请输入分类名称'),
        Length(max=100, message='分类名称不能超过100个字符')
    ])
    description = TextAreaField('分类描述', validators=[
        Length(max=500, message='描述不能超过500个字符')
    ])
    parent_id = SelectField('上级分类', coerce=int, validators=[Optional()])
    sort_order = IntegerField('排序', validators=[
        NumberRange(min=0, message='排序值不能小于0')
    ], default=0)
    is_active = BooleanField('启用', default=True)
    submit = SubmitField('保存')
    
    def __init__(self, *args, **kwargs):
        super(CategoryForm, self).__init__(*args, **kwargs)
        self.parent_id.choices = [(0, '无上级分类')]
        categories = Category.query.filter_by(is_active=True).order_by(Category.sort_order).all()
        for category in categories:
            self.parent_id.choices.append((category.id, category.name))

class AttributeGroupForm(FlaskForm):
    category_id = SelectField('所属分类', coerce=int, validators=[Optional()])  # 暂时设为可选
    name = StringField('大属性名称', validators=[
        DataRequired(message='请输入大属性名称'),
        Length(max=100, message='大属性名称不能超过100个字符')
    ])
    description = TextAreaField('大属性描述', validators=[
        Length(max=500, message='描述不能超过500个字符')
    ])
    display_type = SelectField('显示方式', choices=[
        ('radio', '单选按钮'),
        ('select', '下拉框'),
        ('checkbox', '多选复选框')
    ], default='radio', validators=[DataRequired(message='请选择显示方式')])

    # 多选支持
    is_multiple = BooleanField('允许多选', default=False)
    max_selections = IntegerField('最大选择数量', validators=[
        Optional(),
        NumberRange(min=1, max=20, message='最大选择数量必须在1-20之间')
    ])

    # 自定义输入支持
    allow_custom_input = BooleanField('允许自定义输入', default=False)
    custom_input_type = SelectField('自定义输入类型', choices=[
        ('text', '文本输入'),
        ('number', '数字输入'),
        ('textarea', '长文本输入')
    ], default='text')
    custom_input_label = StringField('自定义选项标签', validators=[
        Length(max=100, message='标签不能超过100个字符')
    ], default='自定义')
    custom_input_placeholder = StringField('自定义输入占位符', validators=[
        Length(max=200, message='占位符不能超过200个字符')
    ])
    custom_validation_pattern = StringField('自定义输入验证正则', validators=[
        Length(max=500, message='正则表达式不能超过500个字符')
    ])
    custom_validation_message = StringField('自定义输入验证失败提示', validators=[
        Length(max=200, message='提示信息不能超过200个字符')
    ])

    # 自定义输入价格计算
    custom_price_modifier_type = SelectField('自定义输入价格计算类型', choices=[
        ('fixed', '固定金额'),
        ('percentage', '百分比'),
        ('formula', '计算公式')
    ], default='fixed')
    custom_price_modifier = DecimalField('自定义输入价格调整', validators=[
        NumberRange(min=-999999.99999, max=999999.99999, message='价格调整值超出范围')
    ], default=0.00000, places=5)
    custom_price_formula = TextAreaField('自定义输入价格公式', validators=[
        Length(max=500, message='公式不能超过500个字符')
    ])

    sort_order = IntegerField('排序', validators=[
        NumberRange(min=0, message='排序值不能小于0')
    ], default=0)
    is_active = BooleanField('启用', default=True)
    submit = SubmitField('保存')

    def __init__(self, *args, **kwargs):
        super(AttributeGroupForm, self).__init__(*args, **kwargs)
        self.category_id.choices = [(0, '请选择分类')]
        try:
            categories = Category.query.filter_by(is_active=True).order_by(Category.sort_order).all()
            for category in categories:
                self.category_id.choices.append((category.id, category.name))
        except Exception:
            # 如果查询失败，保持默认选项
            pass

    def validate_custom_validation_pattern(self, custom_validation_pattern):
        """验证自定义输入的正则表达式"""
        if self.allow_custom_input.data and custom_validation_pattern.data:
            try:
                import re
                re.compile(custom_validation_pattern.data)
            except re.error as e:
                raise ValidationError(f'正则表达式语法错误: {str(e)}')

    def validate_custom_price_formula(self, custom_price_formula):
        """验证自定义输入的价格公式"""
        if (self.allow_custom_input.data and
            self.custom_price_modifier_type.data == 'formula' and
            not custom_price_formula.data):
            raise ValidationError('选择公式计算时必须提供价格公式')

        if custom_price_formula.data:
            # 基本的公式语法检查
            formula = custom_price_formula.data.strip()
            if not formula:
                return

            # 尝试验证公式语法
            try:
                test_env = {
                    'value': 'test',
                    'base_price': 100,
                    'length': 4,
                    'numeric_value': 10,
                    'abs': abs,
                    'max': max,
                    'min': min,
                    'round': round,
                    '__builtins__': {}
                }
                eval(formula, test_env)
            except Exception as e:
                raise ValidationError(f'公式语法错误: {str(e)}')

    def validate_custom_price_modifier(self, custom_price_modifier):
        """验证自定义输入的价格调整值"""
        if (self.allow_custom_input.data and
            self.custom_price_modifier_type.data == 'percentage' and
            custom_price_modifier.data and
            abs(custom_price_modifier.data) > 1000):
            raise ValidationError('百分比调整值不应超过1000%')

class AttributeForm(FlaskForm):
    group_id = SelectField('大属性组', coerce=int, validators=[
        DataRequired(message='请选择大属性组')
    ])
    name = StringField('小属性名称', validators=[
        DataRequired(message='请输入小属性名称'),
        Length(max=100, message='小属性名称不能超过100个字符')
    ])
    value = StringField('小属性值', validators=[
        DataRequired(message='请输入小属性值'),
        Length(max=200, message='小属性值不能超过200个字符')
    ])

    # 输入类型选择
    input_type = SelectField('输入类型', choices=[
        ('select', '下拉选择'),
        ('radio', '单选按钮'),
        ('input', '文本输入'),
        ('textarea', '多行文本'),
        ('checkbox', '复选框')
    ], default='select')

    # 自定义输入相关字段
    is_custom_input = BooleanField('启用自定义输入', default=False)
    input_placeholder = StringField('输入框占位符', validators=[
        Length(max=200, message='占位符不能超过200个字符')
    ])
    default_value = StringField('默认值', validators=[
        Length(max=200, message='默认值不能超过200个字符')
    ])
    validation_pattern = StringField('验证正则表达式', validators=[
        Length(max=500, message='正则表达式不能超过500个字符')
    ])
    validation_message = StringField('验证失败提示', validators=[
        Length(max=200, message='提示信息不能超过200个字符')
    ])

    # 价格调整方式选择
    calculation_type = SelectField('价格计算方式', choices=[
        ('fixed', '固定金额'),
        ('percentage', '百分比'),
        ('formula', '计算公式')
    ], default='fixed')
    
    # 传统价格调整字段
    price_modifier = DecimalField('价格调整', validators=[
        NumberRange(min=-999999.99999, max=999999.99999, message='价格调整值超出范围')
    ], default=0.00000, places=5)
    price_modifier_type = SelectField('调整类型', choices=[
        ('fixed', '固定金额'),
        ('percentage', '百分比')
    ], default='fixed')
    
    # 公式计算字段
    price_formula = TextAreaField('价格公式', validators=[
        Length(max=500, message='公式不能超过500个字符')
    ])
    is_quantity_based = BooleanField('支持用户输入数量', default=False)
    quantity_unit = StringField('数量单位', validators=[
        Length(max=20, message='单位不能超过20个字符')
    ], default='个')
    min_quantity = IntegerField('最小数量', validators=[
        NumberRange(min=1, message='最小数量不能小于1')
    ], default=1)
    max_quantity = IntegerField('最大数量', validators=[
        NumberRange(min=1, message='最大数量不能小于1')
    ], default=9999)
    
    sort_order = IntegerField('排序', validators=[
        NumberRange(min=0, message='排序值不能小于0')
    ], default=0)
    is_active = BooleanField('启用', default=True)
    submit = SubmitField('保存')
    
    def __init__(self, *args, **kwargs):
        super(AttributeForm, self).__init__(*args, **kwargs)
        self.group_id.choices = []
        groups = AttributeGroup.query.filter_by(is_active=True).order_by(AttributeGroup.sort_order).all()
        for group in groups:
            self.group_id.choices.append((group.id, group.name))
    
    def validate_price_formula(self, price_formula):
        """验证价格公式"""
        if self.is_quantity_based.data and not price_formula.data:
            raise ValidationError('启用数量输入时必须提供计算公式')
        
        if price_formula.data:
            # 基本的公式语法检查
            formula = price_formula.data.strip()
            if not formula:
                return
                
            # 检查是否包含必要的变量
            if 'quantity' not in formula and self.is_quantity_based.data:
                raise ValidationError('公式中必须包含 quantity 变量')
            
            # 尝试验证公式语法
            try:
                test_env = {
                    'quantity': 10,
                    'base_price': 100,
                    'abs': abs,
                    'max': max,
                    'min': min,
                    'round': round,
                    '__builtins__': {}
                }
                eval(formula, test_env)
            except Exception as e:
                raise ValidationError(f'公式语法错误: {str(e)}')
    
    def validate_max_quantity(self, max_quantity):
        """验证最大数量"""
        if self.is_quantity_based.data and self.min_quantity.data and max_quantity.data < self.min_quantity.data:
            raise ValidationError('最大数量不能小于最小数量')

class ProductForm(FlaskForm):
    name = StringField('商品名称', validators=[
        DataRequired(message='请输入商品名称'),
        Length(max=200, message='商品名称不能超过200个字符')
    ])
    description = TextAreaField('商品描述')
    category_id = SelectField('商品分类', coerce=int, validators=[Optional()])
    base_price = DecimalField('基础价格', validators=[
        DataRequired(message='请输入基础价格'),
        NumberRange(min=0, message='价格不能小于0')
    ], places=5)
    min_quantity = IntegerField('最小数量', validators=[
        NumberRange(min=1, message='最小数量不能小于1')
    ], default=1)
    max_quantity = IntegerField('最大数量', validators=[
        NumberRange(min=1, message='最大数量不能小于1')
    ], default=999999)
    unit = StringField('单位', validators=[
        Length(max=20, message='单位不能超过20个字符')
    ], default='个')
    image_url = StringField('主图URL', validators=[
        Length(max=500, message='图片URL不能超过500个字符')
    ])
    image_file = FileField('上传主图', validators=[
        FileAllowed(['jpg', 'png', 'gif', 'jpeg', 'webp'], '只允许上传图片文件')
    ])
    # 商品属性选择
    selected_attributes = SelectMultipleField('绑定属性', coerce=int, validators=[Optional()])
    sort_order = IntegerField('显示顺序', default=0)
    is_active = BooleanField('启用', default=True)
    submit = SubmitField('保存')
    
    def __init__(self, *args, **kwargs):
        # 获取分类ID参数
        category_id = kwargs.pop('category_id', None)
        super(ProductForm, self).__init__(*args, **kwargs)

        # 构建层级分类选择项
        self.category_id.choices = [(0, '无分类')]
        self._build_category_choices()

        # 初始化属性选择项
        self.update_attribute_choices(category_id)

    def _build_category_choices(self):
        """构建层级分类选择项"""
        def build_choices(parent_id=None, level=0):
            categories = Category.query.filter_by(parent_id=parent_id, is_active=True)\
                                     .order_by(Category.sort_order, Category.name).all()
            choices = []
            for category in categories:
                # 创建缩进显示层级关系
                indent = '　' * level  # 使用全角空格缩进
                prefix = '└─ ' if level > 0 else ''
                display_name = f"{indent}{prefix}{category.name}"
                
                choices.append((category.id, display_name))
                
                # 递归添加子分类
                child_choices = build_choices(category.id, level + 1)
                choices.extend(child_choices)
            return choices
        
        category_choices = build_choices()
        self.category_id.choices.extend(category_choices)

    def update_attribute_choices(self, category_id=None):
        """更新属性选择项"""
        self.selected_attributes.choices = []

        # 如果指定了分类，只显示该分类的属性组中的属性
        if category_id:
            category = Category.query.get(category_id)
            if category:
                # 获取该分类的属性组（直接关系）
                bound_groups = category.attribute_groups.filter_by(is_active=True).order_by(AttributeGroup.sort_order, AttributeGroup.name).all()
                for group in bound_groups:
                    attributes = Attribute.query.filter_by(group_id=group.id, is_active=True).order_by(Attribute.sort_order, Attribute.name).all()
                    if attributes:
                        for attr in attributes:
                            # 格式：大属性 - 小属性 (属性值)
                            label = f"{group.name} - {attr.name}"
                            if attr.value and attr.value != attr.name:
                                label += f" ({attr.value})"
                            if attr.price_modifier != 0:
                                if attr.price_modifier_type == 'percentage':
                                    label += f" [+{attr.price_modifier}%]"
                                else:
                                    label += f" [+¥{attr.price_modifier}]"
                            self.selected_attributes.choices.append((attr.id, label))
    
    def validate_max_quantity(self, max_quantity):
        if self.min_quantity.data and max_quantity.data < self.min_quantity.data:
            raise ValidationError('最大数量不能小于最小数量')

    def validate_base_price(self, base_price):
        """自定义基础价格验证"""
        if base_price.data is None:
            raise ValidationError('请输入基础价格')
        # 允许0值，所以不需要额外检查

class QuantityDiscountForm(FlaskForm):
    product_id = SelectField('商品', coerce=int, validators=[
        DataRequired(message='请选择商品')
    ])
    min_quantity = IntegerField('最小数量', validators=[
        DataRequired(message='请输入最小数量'),
        NumberRange(min=1, message='最小数量不能小于1')
    ])
    max_quantity = IntegerField('最大数量', validators=[
        NumberRange(min=1, message='最大数量不能小于1')
    ])
    discount_type = SelectField('折扣类型', choices=[
        ('percentage', '百分比折扣'),
        ('fixed', '固定金额减免')
    ], default='percentage')
    discount_value = DecimalField('折扣值', validators=[
        DataRequired(message='请输入折扣值'),
        NumberRange(min=0.00001, message='折扣值必须大于0')
    ], places=5)
    is_active = BooleanField('启用', default=True)
    submit = SubmitField('保存')

    def __init__(self, *args, **kwargs):
        super(QuantityDiscountForm, self).__init__(*args, **kwargs)
        self.product_id.choices = []
        products = Product.query.filter_by(is_active=True).order_by(Product.name).all()
        for product in products:
            self.product_id.choices.append((product.id, product.name))

    def validate_max_quantity(self, max_quantity):
        if self.min_quantity.data and max_quantity.data and max_quantity.data < self.min_quantity.data:
            raise ValidationError('最大数量不能小于最小数量')

    def validate_discount_value(self, discount_value):
        if self.discount_type.data == 'percentage' and discount_value.data >= 100:
            raise ValidationError('百分比折扣不能大于等于100%')
