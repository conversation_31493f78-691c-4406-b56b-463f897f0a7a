#!/usr/bin/env python3
"""
简单的数据库迁移脚本
"""

from app import app
from models import db
from sqlalchemy import text

def main():
    with app.app_context():
        try:
            print('开始数据库迁移...')
            
            # 添加 is_multiple 字段
            try:
                db.session.execute(text('''
                    ALTER TABLE attribute_groups 
                    ADD COLUMN is_multiple TINYINT(1) DEFAULT 0 NOT NULL
                '''))
                print('✓ 添加 is_multiple 字段')
            except Exception as e:
                if 'Duplicate column name' in str(e):
                    print('- is_multiple 字段已存在')
                else:
                    print(f'添加 is_multiple 字段失败: {e}')
            
            # 添加 max_selections 字段
            try:
                db.session.execute(text('''
                    ALTER TABLE attribute_groups 
                    ADD COLUMN max_selections INT DEFAULT NULL
                '''))
                print('✓ 添加 max_selections 字段')
            except Exception as e:
                if 'Duplicate column name' in str(e):
                    print('- max_selections 字段已存在')
                else:
                    print(f'添加 max_selections 字段失败: {e}')
            
            db.session.commit()
            print('✅ 数据库迁移完成！')
            
        except Exception as e:
            db.session.rollback()
            print(f'❌ 迁移失败: {e}')
            raise

if __name__ == '__main__':
    main()
