#!/usr/bin/env python3
"""
数据库迁移脚本：为属性组添加多选支持
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from models import db
from sqlalchemy import text

def upgrade():
    """添加多选支持字段"""
    with app.app_context():
        try:
            # 检查字段是否已存在
            result = db.session.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'attribute_groups' 
                AND column_name IN ('is_multiple', 'max_selections')
            """)).fetchall()
            
            existing_columns = [row[0] for row in result]
            
            # 添加 is_multiple 字段
            if 'is_multiple' not in existing_columns:
                db.session.execute(text("""
                    ALTER TABLE attribute_groups 
                    ADD COLUMN is_multiple BOOLEAN DEFAULT FALSE NOT NULL
                """))
                print("✓ 添加 is_multiple 字段")
            else:
                print("- is_multiple 字段已存在")
            
            # 添加 max_selections 字段
            if 'max_selections' not in existing_columns:
                db.session.execute(text("""
                    ALTER TABLE attribute_groups 
                    ADD COLUMN max_selections INTEGER DEFAULT NULL
                """))
                print("✓ 添加 max_selections 字段")
            else:
                print("- max_selections 字段已存在")
            
            # 更新 display_type 枚举类型，添加 checkbox 选项
            try:
                # 检查是否已经有 checkbox 选项
                result = db.session.execute(text("""
                    SELECT column_type 
                    FROM information_schema.columns 
                    WHERE table_name = 'attribute_groups' 
                    AND column_name = 'display_type'
                """)).fetchone()
                
                if result and 'checkbox' not in result[0]:
                    # 先创建新的枚举类型
                    db.session.execute(text("""
                        ALTER TABLE attribute_groups 
                        MODIFY COLUMN display_type ENUM('radio', 'select', 'checkbox') DEFAULT 'radio' NOT NULL
                    """))
                    print("✓ 更新 display_type 枚举类型，添加 checkbox 选项")
                else:
                    print("- display_type 已包含 checkbox 选项")
                    
            except Exception as e:
                print(f"⚠ 更新 display_type 枚举类型时出现警告: {e}")
                # 对于某些数据库系统，可能需要不同的语法
                pass
            
            db.session.commit()
            print("✅ 多选支持字段添加完成")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 迁移失败: {e}")
            raise

def downgrade():
    """移除多选支持字段"""
    with app.app_context():
        try:
            # 移除添加的字段
            db.session.execute(text("ALTER TABLE attribute_groups DROP COLUMN IF EXISTS is_multiple"))
            db.session.execute(text("ALTER TABLE attribute_groups DROP COLUMN IF EXISTS max_selections"))
            
            # 恢复原始的枚举类型
            try:
                db.session.execute(text("""
                    ALTER TABLE attribute_groups 
                    MODIFY COLUMN display_type ENUM('radio', 'select') DEFAULT 'radio' NOT NULL
                """))
            except Exception as e:
                print(f"⚠ 恢复 display_type 枚举类型时出现警告: {e}")
            
            db.session.commit()
            print("✅ 多选支持字段移除完成")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 回滚失败: {e}")
            raise

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='属性组多选支持迁移')
    parser.add_argument('action', choices=['upgrade', 'downgrade'], 
                       help='执行升级或降级操作')
    
    args = parser.parse_args()
    
    if args.action == 'upgrade':
        print("🚀 开始添加多选支持...")
        upgrade()
    elif args.action == 'downgrade':
        print("🔄 开始移除多选支持...")
        downgrade()
    
    print("✨ 迁移完成！")
