-- 添加订单附件表
-- 执行时间：2025-01-18

-- 创建订单附件表
CREATE TABLE IF NOT EXISTS order_attachments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '存储路径',
    file_size INT NOT NULL COMMENT '文件大小（字节）',
    file_type VARCHAR(10) NOT NULL COMMENT '文件类型（扩展名）',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 外键约束
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_order_id (order_id),
    INDEX idx_file_type (file_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单附件表';

-- 验证表是否创建成功
SELECT 'order_attachments表创建成功' AS message;

-- 显示表结构
DESCRIBE order_attachments;
