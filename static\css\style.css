/* 全局样式 */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* 导航栏样式 */
.navbar-brand {
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* 卡片样式 */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 10px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-img-top {
    border-radius: 10px 10px 0 0;
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* 按钮样式 */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* 轮播图样式 */
.carousel-item {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
}

/* 商品卡片特殊样式 */
.product-card {
    border: 1px solid #e9ecef;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.product-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.price-tag {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--danger-color);
}

/* 表单样式 */
.form-control,
.form-select,
select.form-control,
input.form-control,
textarea.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    color: #333333 !important;
    font-weight: 500;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control::placeholder {
    color: #777777 !important;
}

/* 选择框中的选项样式 */
select.form-control option {
    color: #333333;
    background-color: #ffffff;
    font-weight: 500;
}

/* 暗黑模式下的表单控件 */
[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select,
[data-bs-theme="dark"] select.form-control,
[data-bs-theme="dark"] input.form-control,
[data-bs-theme="dark"] textarea.form-control {
    color: #e6e6e6 !important;
    background-color: rgba(30, 40, 50, 0.9);
}

[data-bs-theme="dark"] .form-control::placeholder {
    color: #aaaaaa !important;
}

[data-bs-theme="dark"] select.form-control option {
    color: #e6e6e6;
    background-color: #2d3748;
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 50px;
    margin: 0 2px;
    border: none;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

/* 面包屑导航 */
.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

/* 购物车徽章 */
.badge {
    font-size: 0.7rem;
}

/* 购物车页面样式 */
.cart-header {
    background: #f8f9fa;
    border-radius: 0;
    margin: 0;
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.cart-item {
    transition: all 0.3s ease;
    border-radius: 0;
    font-size: 0.85rem;
}

.cart-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.cart-item:last-child {
    border-bottom: none !important;
}

.cart-item .quantity-selector {
    margin: 0 auto;
    max-width: 100px;
    border-radius: 12px;
    border: 1px solid #dee2e6;
    overflow: hidden;
    background: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.cart-item .item-price {
    font-size: 0.85rem;
    font-weight: 600;
}

.cart-item img {
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cart-item img:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.cart-item .product-info h6 {
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

.cart-item .product-info h6 a {
    color: #333;
    transition: color 0.3s ease;
}

.cart-item .product-info h6 a:hover {
    color: #007bff;
}

.cart-item .product-info .small {
    font-size: 0.75rem;
}

/* 订单摘要样式 */
.summary-item {
    padding: 0.2rem 0;
    border-bottom: 1px solid #f8f9fa;
    font-size: 0.85rem;
}

.summary-item:last-child {
    border-bottom: none;
}

/* 购物车标题优化 */
.cart-title {
    font-size: 1.3rem;
    font-weight: 600;
}

.cart-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

/* 订单摘要标题 */
.cart-summary-title {
    font-size: 1rem;
    font-weight: 600;
}

/* 总价显示 */
.cart-total-label {
    font-size: 1rem;
}

.cart-total-price {
    font-size: 1.4rem;
}

/* 空购物车样式 */
.empty-cart-icon {
    opacity: 0.6;
    transition: all 0.3s ease;
}

.empty-cart-icon:hover {
    opacity: 0.8;
    transform: scale(1.1);
}

/* 属性选择器 */
.attribute-group {
    margin-bottom: 1rem;
}

.attribute-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
}

.attribute-option {
    padding: 0.35rem 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    position: relative;
    overflow: hidden;
    font-size: 0.85rem;
    min-width: 50px;
    text-align: center;
}

.attribute-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.attribute-option:hover {
    border-color: var(--primary-color);
    background: rgba(13, 110, 253, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.2);
}

.attribute-option:hover::before {
    left: 100%;
}

.attribute-option.selected {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

.attribute-option:active {
    transform: scale(0.98);
}

/* 数量选择器 */
.quantity-selector {
    display: flex;
    align-items: center;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    overflow: hidden;
    width: fit-content;
    max-width: 100px;
    background: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.quantity-btn {
    background: #f8f9fa;
    border: none;
    padding: 0.3rem 0.6rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.75rem;
    color: #495057;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 28px;
    height: 28px;
    border-radius: 0;
    font-weight: 500;
}

.quantity-btn:hover {
    background: #e9ecef;
    color: #495057;
    transform: scale(1.05);
}

.quantity-btn:active {
    transform: scale(0.95);
    background: #dee2e6;
}

.quantity-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: #f8f9fa;
}

.quantity-btn:disabled:hover {
    transform: none;
    background: #f8f9fa;
}

.quantity-input {
    border: none;
    text-align: center;
    width: 44px;
    padding: 0.3rem 0.2rem;
    font-size: 0.85rem;
    height: 28px;
    background: white;
    font-weight: 500;
    color: #495057;
}

.quantity-input:focus {
    outline: none;
    background: #f8f9fa;
}

/* 价格显示 */
.price-display {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 1rem;
    margin: 0.8rem 0;
}

.price-main {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--danger-color);
    transition: all 0.3s ease;
}

.price-unit {
    font-size: 0.9rem;
    color: #6c757d;
    transition: all 0.3s ease;
}

.price-updated {
    transform: scale(1.05);
    color: var(--success-color) !important;
}

/* 折扣信息 */
.discount-info {
    background: linear-gradient(135deg, var(--success-color), #146c43);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    display: inline-block;
    margin-top: 0.5rem;
}

/* 数量折扣样式 */
.quantity-discounts {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 0.8rem;
    border-left: 3px solid var(--primary-color);
    margin-bottom: 0.8rem;
}

.discount-rules {
    margin-bottom: 0.4rem;
}

.discount-rule {
    display: flex;
    align-items: center;
    margin-bottom: 0.4rem;
    padding: 0.2rem 0;
}

.discount-rule:last-child {
    margin-bottom: 0;
}

.discount-rule .badge {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
}

.discount-rule span:last-child {
    font-weight: 600;
    font-size: 0.85rem;
}

/* 属性组选择样式 */
.attribute-groups-selection {
    max-height: 400px;
    overflow-y: auto;
}

/* 属性组卡片样式 */
.attribute-group-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.attribute-group-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-color: var(--primary-color);
}

/* 组标题样式 */
.group-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.group-title {
    display: flex;
    align-items: center;
    font-size: 1rem;
    color: #495057;
}

.group-title strong {
    color: var(--primary-color);
    margin-right: 0.5rem;
}

.group-actions {
    display: flex;
    gap: 0.5rem;
}

.group-actions .btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-weight: 500;
}

/* 组内容样式 */
.group-content {
    padding: 1.25rem;
    background: #ffffff;
    border-radius: 0 0 12px 12px;
}

/* 属性项样式 */
.attribute-item {
    transition: all 0.2s ease;
}

.attribute-item .form-check {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    margin: 0;
    transition: all 0.2s ease;
    cursor: pointer;
}

.attribute-item .form-check:hover {
    background: rgba(13, 110, 253, 0.05);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.attribute-item .form-check-input {
    margin-top: 0.25rem;
}

.attribute-item .form-check-input:checked + .form-check-label {
    color: var(--primary-color);
    font-weight: 600;
}

.attribute-item .form-check-input:checked ~ * {
    background: rgba(13, 110, 253, 0.1);
}

.attribute-item .form-check-label {
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0;
}

/* 选中状态的属性项 */
.attribute-item .form-check:has(.form-check-input:checked) {
    background: rgba(13, 110, 253, 0.1);
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.15);
}

/* 商品详情页面优化 */
.product-detail h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.product-detail .form-label {
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

.product-detail .mb-4 {
    margin-bottom: 1rem !important;
}

.product-detail .mb-3 {
    margin-bottom: 0.8rem !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .carousel-item {
        min-height: 300px;
    }

    .display-4 {
        font-size: 2rem;
    }

    .lead {
        font-size: 1rem;
    }

    .product-card {
        margin-bottom: 1rem;
    }

    .attribute-options {
        justify-content: center;
    }

    .price-display {
        text-align: center;
        padding: 0.8rem;
    }

    .quantity-selector {
        max-width: 120px;
    }

    .attribute-option {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 成功/错误消息样式 */
.alert {
    border-radius: 10px;
    border: none;
}

.alert-success {
    background: linear-gradient(135deg, var(--success-color), #146c43);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, var(--danger-color), #b02a37);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, var(--info-color), #0aa2c0);
    color: white;
}

/* 页脚样式 */
footer {
    background: linear-gradient(135deg, var(--dark-color), #495057) !important;
}

footer a:hover {
    color: var(--primary-color) !important;
}

/* 商品详情页样式 - 统一主题色 */
.product-detail {
    background: #f8f9fa;
    min-height: 100vh;
    padding-top: 1rem;
}

.product-detail .container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    padding: 2rem;
    margin-bottom: 2rem;
}

/* 商品图片区域 */
.product-image-container {
    position: sticky;
    top: 2rem;
}

.main-image-wrapper {
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.main-product-image {
    width: 100%;
    height: 350px;
    object-fit: cover;
    display: block;
}

.image-thumbnails {
    display: flex;
    gap: 8px;
    justify-content: flex-start;
}

.thumbnail-item {
    width: 60px;
    height: 60px;
    border: 2px solid #e5e5e5;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    transition: border-color 0.3s;
}

.thumbnail-item:hover {
    border-color: var(--primary-color);
}

.thumbnail-item.active {
    border-color: var(--primary-color);
}

.thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 商品信息区域 */
.product-info {
    padding-left: 2rem;
}

.product-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.product-subtitle {
    font-size: 0.95rem;
    color: #666;
    margin-bottom: 1.5rem;
}

/* 价格区域 */
.price-section {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.2rem;
}

.price-main {
    display: flex;
    align-items: baseline;
    margin-bottom: 0.5rem;
}

.price-symbol {
    font-size: 1.2rem;
    color: var(--danger-color);
    font-weight: 600;
}

.price-value {
    font-size: 2rem;
    color: var(--danger-color);
    font-weight: 700;
    margin: 0 0.2rem;
    transition: all 0.3s ease;
}

.price-unit {
    font-size: 0.9rem;
    color: #6c757d;
}

.price-info {
    font-size: 0.85rem;
    color: #6c757d;
    transition: all 0.3s ease;
}

/* 价格更新动画效果 */
.price-updated {
    animation: priceHighlight 0.4s ease-in-out;
}

@keyframes priceHighlight {
    0% {
        transform: scale(1);
        background-color: transparent;
    }
    50% {
        transform: scale(1.02);
        background-color: rgba(0, 123, 255, 0.1);
        border-radius: 4px;
    }
    100% {
        transform: scale(1);
        background-color: transparent;
    }
}

/* 规格选择 */
.spec-section {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 1.5rem;
}

.spec-group {
    margin-bottom: 1rem;
}

.spec-label {
    font-size: 0.95rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.spec-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 8px;
}

.spec-option {
    padding: 10px 16px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.15s ease;
    font-size: 0.9rem;
    background: white;
    display: flex;
    align-items: center;
    gap: 4px;
    user-select: none;
    min-width: 60px;
    justify-content: center;
    position: relative;
}

/* 隐藏原生的radio和checkbox输入框 */
.spec-option input[type="radio"],
.spec-option input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
    margin: 0;
    padding: 0;
}

/* 确保label标签填满整个容器 */
.spec-option .spec-option-label {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
    font-size: inherit;
    font-weight: inherit;
    color: inherit;
}

.spec-option:hover {
    border-color: var(--primary-color);
    background: rgba(13, 110, 253, 0.08);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
}

.spec-option.selected {
    border-color: var(--primary-color) !important;
    background: var(--primary-color) !important;
    color: white !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.3) !important;
}

.spec-option:active {
    transform: translateY(0);
    transition: all 0.1s ease;
}

.spec-text {
    font-weight: 500;
}

.spec-price {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* 数量选择 */
.quantity-section {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 1.5rem;
}

.quantity-label {
    font-size: 0.95rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.product-detail .quantity-selector {
    display: flex;
    align-items: center;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    overflow: hidden;
}

.product-detail .quantity-btn {
    background: #f8f8f8;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    transition: background 0.3s;
    font-size: 0.9rem;
    color: #666;
}

.product-detail .quantity-btn:hover {
    background: #e8e8e8;
}

.product-detail .quantity-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.product-detail .quantity-input {
    border: none;
    text-align: center;
    width: 60px;
    padding: 8px 4px;
    font-size: 0.9rem;
    background: white;
}

.product-detail .quantity-input:focus {
    outline: none;
}

.quantity-limit {
    font-size: 0.85rem;
    color: #999;
}

/* 操作按钮 */
.action-section {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 1.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
}

.btn-add-cart {
    flex: 1;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-add-cart:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

.btn-buy-now {
    flex: 1;
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-buy-now:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

.btn-login {
    flex: 1;
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-login:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

/* 折扣信息 */
.discount-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
}

.discount-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.discount-list {
    margin-bottom: 1rem;
}

.discount-list:last-child {
    margin-bottom: 0;
}

.discount-type {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.discount-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.4rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.discount-item:last-child {
    border-bottom: none;
}

.discount-range {
    font-size: 0.85rem;
    color: #333;
    background: #e9ecef;
    padding: 2px 8px;
    border-radius: 3px;
}

.discount-value {
    font-size: 0.85rem;
    color: #28a745;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .product-info {
        padding-left: 0;
        margin-top: 1rem;
    }

    .main-product-image {
        height: 250px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .price-value {
        font-size: 1.5rem;
    }

    .spec-options {
        justify-content: flex-start;
    }

    .custom-input-wrapper {
        margin-top: 8px;
        padding: 8px;
    }

    .custom-input-wrapper .form-control {
        font-size: 13px;
        padding: 6px 10px;
    }

    .spec-group .custom-input-wrapper {
        max-width: 100%;
    }
}

/* 自定义输入框样式优化 */
.custom-input-wrapper {
    margin-top: 10px;
    margin-left: 0;
    padding: 6px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    transition: all 0.3s ease;
    max-width: 250px;
    width: auto;
}

.custom-input-wrapper .input-group {
    max-width: 100%;
    align-items: stretch;
}

.custom-input-wrapper .form-control {
    font-size: 12px;
    padding: 4px 6px;
    border-radius: 3px;
    border: 1px solid #ced4da;
    height: 26px;
    line-height: 1.2;
}

.custom-input-wrapper .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.1rem rgba(13, 110, 253, 0.15);
}

.custom-input-wrapper .btn {
    padding: 4px 6px;
    font-size: 10px;
    border-radius: 3px;
    line-height: 1.2;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-left: none;
}

.custom-input-wrapper .invalid-feedback {
    font-size: 10px;
    margin-top: 2px;
}

.custom-input-wrapper small {
    font-size: 9px;
    color: #6c757d;
    margin-top: 2px;
    display: block;
}

/* 自定义选项样式优化 */
.spec-option[data-custom="true"] {
    background-color: #fff3cd;
    border-color: #ffc107;
    color: #856404;
    font-weight: 500;
    font-size: 13px;
}

.spec-option[data-custom="true"]:hover {
    background-color: #ffeaa7;
    border-color: #ffb300;
    color: #856404;
}

.spec-option[data-custom="true"].selected {
    background-color: #ffc107;
    border-color: #ffb300;
    color: #212529;
    font-weight: 600;
}

/* 单选按钮模式的自定义输入优化 */
.spec-group .custom-input-wrapper {
    margin-left: 0;
    margin-right: 0;
}

.spec-group .custom-input-wrapper .input-group {
    display: flex;
    align-items: center;
}

.spec-group .custom-input-wrapper .form-control {
    flex: 1;
    min-width: 0;
}

.spec-group .custom-input-wrapper .btn-outline-secondary {
    border-left: none;
    padding: 6px 8px;
    color: #6c757d;
    font-size: 12px;
}

.spec-group .custom-input-wrapper .btn-outline-secondary:hover {
    background-color: #e9ecef;
    border-color: #ced4da;
    color: #495057;
}

/* 下拉框模式的自定义输入优化 */
.spec-select-container .custom-input-wrapper {
    max-width: 100%;
}

/* 规格选项尺寸优化 */
.spec-option {
    font-size: 13px;
    padding: 8px 12px;
    min-width: 50px;
}

.spec-option-label {
    font-size: 13px;
    line-height: 1.3;
}
