{% extends "admin/base.html" %}

{% block title %}{{ title }} - 管理后台{% endblock %}

{% block extra_css %}
<style>
.attribute-group-card {
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem !important;
}

.group-header {
    background-color: #f8f9fa;
    padding: 0.4rem 0.6rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.group-title {
    font-size: 0.85rem;
    margin: 0;
}

.group-actions .btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.75rem;
}

.group-content {
    padding: 0.4rem 0.6rem;
}

.attribute-item {
    margin-bottom: 0.2rem;
}

.form-check {
    margin-bottom: 0;
}

.form-check-label {
    font-size: 0.8rem;
    margin-bottom: 0;
}

.form-label-sm {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.form-control-sm, .form-select-sm {
    font-size: 0.875rem;
}

.input-group-sm .input-group-text {
    font-size: 0.875rem;
}

.badge {
    font-size: 0.7rem;
}

.category-preselect-info {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 1px solid #c3e6cb;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin.products') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回列表
        </a>
    </div>
</div>

<!-- 预选分类提示 -->
{% if form.category_id.data and form.category_id.data != 0 %}
<div class="category-preselect-info">
    <div class="d-flex align-items-center">
        <i class="fas fa-info-circle text-success me-3 fa-lg"></i>
        <div>
            <h6 class="mb-1 text-success">
                <i class="fas fa-check-circle me-1"></i>已自动选择分类
            </h6>
            <p class="mb-0 text-dark">
                系统已为您预选分类并自动加载对应的属性组，您可以直接选择需要的属性。
                如需更改分类，请在下方的分类选择中重新选择。
            </p>
        </div>
    </div>
</div>
{% endif %}

<div class="row">
    <div class="col-md-4">
        <!-- 帮助信息卡片 -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>帮助信息
                </h6>
            </div>
            <div class="card-body">
                <!-- 商品信息说明 -->
                <div class="mb-4">
                    <h6 class="text-primary">
                        <i class="fas fa-box me-2"></i>商品信息说明
                    </h6>
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="fas fa-tag text-success me-2"></i>
                            <strong>商品名称</strong>：商品的标题，必填项
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-align-left text-info me-2"></i>
                            <strong>商品描述</strong>：详细的商品介绍
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-folder text-warning me-2"></i>
                            <strong>分类</strong>：商品所属的分类
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-dollar-sign text-success me-2"></i>
                            <strong>基础价格</strong>：商品的起始价格
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-sort-numeric-up text-secondary me-2"></i>
                            <strong>数量限制</strong>：购买数量的范围
                        </li>
                    </ul>
                </div>

                <!-- 价格计算 -->
                <div class="mb-4">
                    <h6 class="text-primary">
                        <i class="fas fa-calculator me-2"></i>价格计算
                    </h6>
                    <div class="bg-light p-3 rounded">
                        <div class="small">
                            <div class="mb-2">
                                <strong class="text-success">最终价格</strong> = 基础价格 + 属性调整
                            </div>
                            <ul class="list-unstyled mb-0">
                                <li><i class="fas fa-check text-success me-2"></i>支持数量折扣规则</li>
                                <li><i class="fas fa-check text-success me-2"></i>可设置属性价格调整</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 状态说明 -->
                <div class="mb-0">
                    <h6 class="text-primary">
                        <i class="fas fa-toggle-on me-2"></i>状态说明
                    </h6>
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <span class="badge bg-success me-2">启用</span>
                            商品在前台显示
                        </li>
                        <li class="mb-2">
                            <span class="badge bg-secondary me-2">禁用</span>
                            商品不在前台显示
                        </li>
                        <li class="mb-2">
                            <span class="badge bg-warning me-2">推荐</span>
                            在推荐位置显示
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        {% if product %}
        <!-- 当前商品信息卡片 -->
        <div class="card mt-3">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-box me-2"></i>当前商品信息
                </h6>
            </div>
            <div class="card-body">
                <!-- 商品图片 -->
                <div class="text-center mb-3">
                    <img src="{{ product.get_main_image() }}" alt="{{ product.name }}"
                         class="img-thumbnail border-primary" style="max-width: 150px; max-height: 150px; object-fit: cover;">
                </div>

                <!-- 商品基本信息 -->
                <div class="small">
                    <div class="row mb-2">
                        <div class="col-12">
                            <i class="fas fa-tag text-primary me-2"></i>
                            <strong>{{ product.name }}</strong>
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-12">
                            <i class="fas fa-dollar-sign text-success me-2"></i>
                            <strong>基础价格</strong>：<span class="text-success">¥{{ "%.2f"|format(product.base_price) }}</span>
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-12">
                            <i class="fas fa-folder text-warning me-2"></i>
                            <strong>分类</strong>：{{ product.category.name if product.category else '未分类' }}
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-12">
                            <i class="fas fa-toggle-on text-info me-2"></i>
                            <strong>状态</strong>：
                            <span class="badge bg-{{ 'success' if product.is_active else 'secondary' }}">
                                {{ '启用' if product.is_active else '禁用' }}
                            </span>
                        </div>
                    </div>

                    <div class="row mb-0">
                        <div class="col-12">
                            <i class="fas fa-clock text-muted me-2"></i>
                            <strong>创建时间</strong>：{{ product.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}

                    <!-- 基本信息 -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-2">
                                {{ form.name.label(class="form-label form-label-sm") }}
                                {{ form.name(class="form-control form-control-sm") }}
                                {% if form.name.errors %}
                                <div class="text-danger small">
                                    {% for error in form.name.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-2">
                                {{ form.category_id.label(class="form-label form-label-sm") }}
                                {% if form.category_id.data and form.category_id.data != 0 %}
                                <!-- 预选分类时显示锁定提示但保留select元素 -->
                                <div class="alert alert-light border d-flex align-items-center" style="margin-bottom: 0.5rem; padding: 0.5rem;">
                                    <i class="fas fa-lock text-success me-2"></i>
                                    <div class="flex-grow-1">
                                        <strong class="text-success">已锁定分类：</strong>
                                        {% for choice in form.category_id.choices %}
                                            {% if choice[0] == form.category_id.data %}
                                                <span class="text-dark">{{ choice[1] }}</span>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                    <small class="text-muted">由快捷按钮预选</small>
                                </div>
                                <!-- 保留select元素但设为disabled -->
                                {{ form.category_id(class="form-select form-select-sm", id="category_id", disabled=true) }}
                                <!-- 添加隐藏字段保存分类ID -->
                                <input type="hidden" name="category_id" value="{{ form.category_id.data }}">
                                <div class="form-text small">
                                    <i class="fas fa-info-circle me-1"></i>分类已锁定，如需更改请返回商品列表重新选择
                                </div>
                                {% else %}
                                <!-- 正常的分类选择 -->
                                <div class="position-relative">
                                    <!-- 分类搜索输入框 -->
                                    <div class="mb-1">
                                        <input type="text" id="categorySearch" class="form-control form-control-sm" 
                                               placeholder="搜索分类..." autocomplete="off">
                                    </div>
                                    <!-- 分类选择下拉框 -->
                                    <div class="category-select-wrapper">
                                        {{ form.category_id(class="form-select form-select-sm", id="categorySelect") }}
                                        <div class="form-text small">
                                            <i class="fas fa-info-circle me-1"></i>分类按层级显示，可搜索快速定位
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                {% if form.category_id.errors %}
                                <div class="text-danger small">
                                    {% for error in form.category_id.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-2">
                        {{ form.description.label(class="form-label form-label-sm") }}
                        {{ form.description(class="form-control form-control-sm", rows="3") }}
                        {% if form.description.errors %}
                        <div class="text-danger small">
                            {% for error in form.description.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <!-- 价格和数量 -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-2">
                                <label for="base_price" class="form-label form-label-sm">
                                    基础价格 <span class="text-danger">*</span>
                                </label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">¥</span>
                                    {{ form.base_price(class="form-control", step="0.001", placeholder="可输入0或0.001等", min="0") }}
                                </div>
                                {% if form.base_price.errors %}
                                <div class="text-danger small">
                                    {% for error in form.base_price.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-2">
                                {{ form.min_quantity.label(class="form-label form-label-sm") }}
                                {{ form.min_quantity(class="form-control form-control-sm") }}
                                {% if form.min_quantity.errors %}
                                <div class="text-danger small">
                                    {% for error in form.min_quantity.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-2">
                                {{ form.max_quantity.label(class="form-label form-label-sm") }}
                                {{ form.max_quantity(class="form-control form-control-sm") }}
                                {% if form.max_quantity.errors %}
                                <div class="text-danger small">
                                    {% for error in form.max_quantity.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-2">
                                {{ form.unit.label(class="form-label form-label-sm") }}
                                {{ form.unit(class="form-control form-control-sm") }}
                                {% if form.unit.errors %}
                                <div class="text-danger small">
                                    {% for error in form.unit.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- 图片上传区域 -->
                    <div class="mb-2">
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.image_file.label(class="form-label form-label-sm") }}
                                {{ form.image_file(class="form-control form-control-sm image-input") }}
                                {% if form.image_file.errors %}
                                <div class="text-danger small">
                                    {% for error in form.image_file.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text small">支持 JPG、PNG、GIF、WEBP 格式</div>
                            </div>
                            <div class="col-md-6">
                                <!-- 图片预览区域 -->
                                <div class="image-preview-container">
                                    {% if product and product.image_url and not product.image_url.startswith('https://via.placeholder.com') %}
                                    <div class="current-image">
                                        <label class="form-label form-label-sm">当前图片</label>
                                        <div class="position-relative d-inline-block">
                                            <img src="{{ product.get_main_image() }}" alt="当前图片"
                                                 class="img-thumbnail" style="max-width: 80px; max-height: 80px; object-fit: cover;">
                                            <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0"
                                                    onclick="removeCurrentImage()" title="删除当前图片">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    {% endif %}

                                    <!-- 新图片预览 -->
                                    <div class="image-preview"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 商品属性绑定 -->
                    <div class="mb-2">
                        {{ form.selected_attributes.label(class="form-label form-label-sm") }}

                        <!-- 隐藏字段存储已选中的属性（用于编辑模式） -->
                        {% if form.selected_attributes.data %}
                        <div id="selected-attributes-data" style="display: none;">
                            {% for attr_id in form.selected_attributes.data %}
                            <input type="hidden" name="selected_attributes" value="{{ attr_id }}">
                            {% endfor %}
                        </div>
                        {% endif %}

                        <div id="attributes-container" class="border rounded p-2" style="max-height: 350px; overflow-y: auto; font-size: 0.9rem;">
                            <div id="attributes-loading" class="text-center text-muted py-3" style="display: none;">
                                <i class="fas fa-spinner fa-spin me-2"></i>正在加载属性...
                            </div>

                            <div id="no-category-message" class="text-center text-muted py-3" style="display: none;">
                                <i class="fas fa-info-circle me-2"></i>请先选择商品分类
                            </div>

                            <div id="no-attributes-message" class="text-center text-muted py-3" style="display: none;">
                                <i class="fas fa-info-circle me-2"></i>该分类暂无可用属性
                                <br><small>请先为该分类绑定属性组</small>
                            </div>

                            <!-- 动态加载的属性内容容器 -->
                            <div id="attributes-content">
                                <!-- 属性将通过JavaScript动态加载 -->
                            </div>
                        </div>
                        {% if form.selected_attributes.errors %}
                        <div class="text-danger small">
                            {% for error in form.selected_attributes.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text">
                            <i class="fas fa-lightbulb me-1"></i>
                            选择要绑定到此商品的属性，用户在前台可以选择这些属性。属性按组分类显示，支持按组批量选择。
                        </div>
                    </div>

                    <!-- 状态和排序 -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-2">
                                {{ form.sort_order.label(class="form-label form-label-sm") }}
                                {{ form.sort_order(class="form-control form-control-sm") }}
                                {% if form.sort_order.errors %}
                                <div class="text-danger small">
                                    {% for error in form.sort_order.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-2">
                                <label class="form-label form-label-sm">状态</label>
                                <div class="form-check">
                                    {{ form.is_active(class="form-check-input") }}
                                    {{ form.is_active.label(class="form-check-label small") }}
                                </div>
                                {% if form.is_active.errors %}
                                <div class="text-danger small">
                                    {% for error in form.is_active.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('admin.products') }}" class="btn btn-secondary">取消</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 删除当前图片
function removeCurrentImage() {
    if (confirm('确定要删除当前图片吗？')) {
        const currentImageDiv = document.querySelector('.current-image');
        if (currentImageDiv) {
            currentImageDiv.style.display = 'none';
            // 添加隐藏字段标记删除
            const form = document.querySelector('form');
            let deleteInput = document.querySelector('input[name="delete_current_image"]');
            if (!deleteInput) {
                deleteInput = document.createElement('input');
                deleteInput.type = 'hidden';
                deleteInput.name = 'delete_current_image';
                deleteInput.value = '1';
                form.appendChild(deleteInput);
            }
        }
    }
}

// 图片预览增强
function previewImage(input) {
    const previewContainer = document.querySelector('.image-preview');

    if (input.files && input.files[0]) {
        const file = input.files[0];

        // 检查文件大小
        const maxSize = 16 * 1024 * 1024; // 16MB
        if (file.size > maxSize) {
            alert('文件大小不能超过 16MB');
            input.value = '';
            previewContainer.innerHTML = '';
            return;
        }

        // 检查文件类型
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('只支持 JPG、PNG、GIF、WEBP 格式的图片');
            input.value = '';
            previewContainer.innerHTML = '';
            return;
        }

        // 只显示文件信息，不显示图片预览
        previewContainer.innerHTML = `
            <label class="form-label">已选择文件</label>
            <div class="small text-muted">
                文件名: ${file.name}<br>
                文件大小: ${formatFileSize(file.size)}
            </div>
        `;
    } else {
        previewContainer.innerHTML = '';
    }
}

// 绑定事件
document.addEventListener('DOMContentLoaded', function() {
    // 初始化已选中的属性
    initializeSelectedAttributes();

    // 文件输入事件
    const fileInput = document.querySelector('.image-input');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            previewImage(this);
        });
    }

    // 分类选择变化事件
    const categorySelect = document.getElementById('categorySelect') || document.getElementById('category_id');
    if (categorySelect) {
        // 只为非disabled的元素添加change事件监听
        if (!categorySelect.disabled) {
        categorySelect.addEventListener('change', function() {
            const categoryId = this.value;
            
            // 清理所有现有的属性checkbox和隐藏字段
            clearAllAttributeInputs();
            
            // 加载新分类的属性
            loadAttributesByCategory(categoryId);
        });
        }

        // 页面加载时如果已选择分类，立即加载对应属性（无论是否disabled）
        const currentCategoryId = categorySelect.value;
        if (currentCategoryId && currentCategoryId !== '0') {
            console.log('页面加载检测到分类ID:', currentCategoryId, '元素disabled状态:', categorySelect.disabled);
            
            // 检查是否是编辑模式（有已选属性数据）
            const hasExistingData = document.querySelector('#selected-attributes-data');
            
            if (!hasExistingData) {
                // 新建模式：清理可能存在的旧数据
                clearAllAttributeInputs();
            }
            // 无论编辑模式还是新建模式，都加载属性（这对锁定分类特别重要）
            loadAttributesByCategory(currentCategoryId);
        }
    }
});

// 清理所有属性相关的输入框
function clearAllAttributeInputs() {
    console.log('=== 开始清理所有属性输入框 ===');
    
    // 重置全局状态
    globalSelectedAttributes = [];
    
    // 清理动态生成的checkbox
    const attributesContent = document.getElementById('attributes-content');
    if (attributesContent) {
        attributesContent.innerHTML = '';
    }
    
    // 使用强化的清理函数彻底清理隐藏字段
    updateHiddenFields();
    
    console.log('=== 所有属性输入框清理完成 ===');
}

// 批量选择属性组功能
function toggleGroupSelection(groupName, selectAll) {
    const groupClass = 'group-' + groupName.replace(/\s+/g, '_');
    const checkboxes = document.querySelectorAll('.form-check-input.' + groupClass);
    
    checkboxes.forEach(checkbox => {
        const wasChecked = checkbox.checked;
        checkbox.checked = selectAll;
        
        // 更新全局选中状态
        const attrId = parseInt(checkbox.value);
        if (selectAll) {
            if (!globalSelectedAttributes.includes(attrId)) {
                globalSelectedAttributes.push(attrId);
            }
        } else {
            const index = globalSelectedAttributes.indexOf(attrId);
            if (index > -1) {
                globalSelectedAttributes.splice(index, 1);
            }
        }
        
        // 如果状态发生了变化，手动触发change事件以保持一致性
        if (wasChecked !== selectAll) {
            checkbox.dispatchEvent(new Event('change'));
        }
    });
    
    console.log('Group selection updated:', groupName, selectAll ? '全选' : '取消', 'Selected attributes:', globalSelectedAttributes.sort());
    
    // 立即同步更新隐藏字段
    updateHiddenFields();
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 动态加载属性功能
function loadAttributesByCategory(categoryId) {
    const container = document.getElementById('attributes-container');
    const loadingDiv = document.getElementById('attributes-loading');
    const noCategoryDiv = document.getElementById('no-category-message');
    const noAttributesDiv = document.getElementById('no-attributes-message');
    const contentDiv = document.getElementById('attributes-content');

    // 隐藏所有状态
    loadingDiv.style.display = 'none';
    noCategoryDiv.style.display = 'none';
    noAttributesDiv.style.display = 'none';
    contentDiv.style.display = 'none';

    if (!categoryId || categoryId == '0') {
        noCategoryDiv.style.display = 'block';
        return;
    }

    // 显示加载状态
    loadingDiv.style.display = 'block';

    // 发送AJAX请求
    fetch(`/admin/api/attributes-by-category/${categoryId}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                          document.querySelector('#csrf_token')?.value || ''
        },
        credentials: 'same-origin'
    })
        .then(response => response.json())
        .then(data => {
            loadingDiv.style.display = 'none';

            if (data.success && data.attributes && data.attributes.length > 0) {
                renderAttributes(data.attributes);
                contentDiv.style.display = 'block';
            } else {
                noAttributesDiv.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('加载属性失败:', error);
            loadingDiv.style.display = 'none';
            noAttributesDiv.style.display = 'block';
        });
}

// 全局变量存储已选中的属性ID
let globalSelectedAttributes = [];

// 初始化已选中的属性ID
function initializeSelectedAttributes() {
    globalSelectedAttributes = [];

    // 从隐藏字段获取已选中的属性（编辑模式）
    const hiddenInputs = document.querySelectorAll('#selected-attributes-data input[type="hidden"]');
    hiddenInputs.forEach(input => {
        const attrId = parseInt(input.value);
        if (!isNaN(attrId) && !globalSelectedAttributes.includes(attrId)) {
            globalSelectedAttributes.push(attrId);
        }
    });

    console.log('Initialized selected attributes from hidden inputs:', globalSelectedAttributes.sort((a,b) => a-b));
    
    // 确保隐藏字段与全局状态同步
    updateHiddenFields();
}

// 获取当前已选中的属性ID
function getCurrentSelectedAttributes() {
    // 更新全局选中状态
    const currentCheckboxes = document.querySelectorAll('input[name="selected_attributes"]:checked');
    globalSelectedAttributes = [];
    currentCheckboxes.forEach(checkbox => {
        globalSelectedAttributes.push(parseInt(checkbox.value));
    });

    return globalSelectedAttributes;
}

// 渲染属性列表
function renderAttributes(attributesData) {
    const contentDiv = document.getElementById('attributes-content');

    // 渲染时使用全局已选中状态，不要从checkbox重新读取
    console.log('Rendering attributes with pre-selected IDs:', globalSelectedAttributes.sort((a,b) => a-b));

    // attributesData现在是按组分组的数据结构
    // 每个元素包含：group_name, group_id, attributes数组

    let html = '<div class="attribute-groups-selection">';

    attributesData.forEach(groupData => {
        const groupName = groupData.group_name;
        const groupAttrs = groupData.attributes; // 这里是该组的属性数组
        const groupClass = 'group-' + groupName.replace(/\s+/g, '_');

        html += `
            <div class="attribute-group-card">
                <div class="group-header">
                    <div class="group-title">
                        <i class="fas fa-folder-open text-primary me-2"></i>
                        <strong>${groupName}</strong>
                        <span class="badge bg-light text-dark ms-2">${groupAttrs.length}项</span>
                    </div>
                    <div class="group-actions">
                        <button type="button" class="btn btn-outline-primary btn-sm"
                                onclick="toggleGroupSelection('${groupName}', true)">
                            <i class="fas fa-check-square me-1"></i>全选
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm"
                                onclick="toggleGroupSelection('${groupName}', false)">
                            <i class="fas fa-square me-1"></i>取消
                        </button>
                    </div>
                </div>
                <div class="group-content">
                    <div class="row">`;

        groupAttrs.forEach(attr => {
            // 检查当前属性是否应该被选中（使用全局状态）
            const isChecked = globalSelectedAttributes.includes(attr.id);
            const checkedAttr = isChecked ? 'checked' : '';

            html += `
                <div class="col-md-6 mb-1">
                    <div class="attribute-item">
                        <div class="form-check">
                            <input class="form-check-input ${groupClass}" type="checkbox"
                                   name="selected_attributes"
                                   value="${attr.id}"
                                   id="attr_${attr.id}" ${checkedAttr}>
                            <label class="form-check-label" for="attr_${attr.id}">
                                ${attr.name}`;

            if (attr.value && attr.value !== attr.name) {
                html += ` (${attr.value})`;
            }

            if (attr.price_modifier != 0) {
                if (attr.price_modifier_type === 'percentage') {
                    html += ` <span class="text-success">[+${attr.price_modifier}%]</span>`;
                } else {
                    html += ` <span class="text-success">[+¥${attr.price_modifier}]</span>`;
                }
            }

            html += `
                            </label>
                        </div>
                    </div>
                </div>`;
        });

        html += `
                    </div>
                </div>
            </div>`;
    });

    html += '</div>';
    contentDiv.innerHTML = html;
    
    // 为所有checkbox添加变化事件监听器
    const checkboxes = contentDiv.querySelectorAll('input[name="selected_attributes"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const attrId = parseInt(this.value);
            if (this.checked) {
                // 添加到全局选中状态
                if (!globalSelectedAttributes.includes(attrId)) {
                    globalSelectedAttributes.push(attrId);
                }
                console.log('Added attribute:', attrId, 'Current selected:', globalSelectedAttributes.sort());
            } else {
                // 从全局选中状态中移除
                const index = globalSelectedAttributes.indexOf(attrId);
                if (index > -1) {
                    globalSelectedAttributes.splice(index, 1);
                }
                console.log('Removed attribute:', attrId, 'Current selected:', globalSelectedAttributes.sort());
            }
            
            // 立即同步更新隐藏字段！！！
            updateHiddenFields();
        });
    });
    
    console.log('Attributes rendered. Checked boxes count:', contentDiv.querySelectorAll('input:checked').length);
}

// 立即同步更新隐藏字段的函数
function updateHiddenFields() {
    const form = document.querySelector('form');
    
    console.log('=== updateHiddenFields 开始执行 ===');
    console.log('执行前全局状态:', globalSelectedAttributes.sort());
    
    // 彻底清理所有可能的selected_attributes字段
    // 1. 清理所有隐藏的selected_attributes字段
    const existingHiddenInputs = form.querySelectorAll('input[name="selected_attributes"][type="hidden"]');
    console.log('清理现有隐藏字段数量:', existingHiddenInputs.length);
    existingHiddenInputs.forEach(input => input.remove());
    
    // 2. 清理初始编辑模式的数据容器中的字段
    const editModeContainer = document.getElementById('selected-attributes-data');
    if (editModeContainer) {
        const editModeInputs = editModeContainer.querySelectorAll('input[name="selected_attributes"]');
        console.log('清理编辑模式容器中的字段数量:', editModeInputs.length);
        editModeInputs.forEach(input => input.remove());
    }
    
    // 3. 清理表单中可能残留的其他selected_attributes字段
    const allSelectedAttrInputs = form.querySelectorAll('input[name="selected_attributes"]:not([type="checkbox"])');
    console.log('清理其他残留字段数量:', allSelectedAttrInputs.length);
    allSelectedAttrInputs.forEach(input => input.remove());
    
    // 4. 多重去重：确保全局状态中没有重复，并转换为整数
    const beforeDedup = globalSelectedAttributes.slice();
    globalSelectedAttributes = [...new Set(globalSelectedAttributes.map(id => parseInt(id)).filter(id => !isNaN(id)))];
    
    if (beforeDedup.length !== globalSelectedAttributes.length) {
        console.log('警告：发现并清理了重复属性');
        console.log('去重前:', beforeDedup.sort());
        console.log('去重后:', globalSelectedAttributes.sort());
    }
    
    // 5. 为当前全局选中的每个属性ID创建新的隐藏字段
    const createdFields = new Set(); // 防止意外的重复创建
    globalSelectedAttributes.forEach(attrId => {
        if (!createdFields.has(attrId)) {
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'selected_attributes';
            hiddenInput.value = attrId;
            hiddenInput.setAttribute('data-source', 'dynamic'); // 标记来源
            hiddenInput.setAttribute('data-attr-id', attrId); // 便于调试
            form.appendChild(hiddenInput);
            createdFields.add(attrId);
            console.log('创建隐藏字段 - 属性ID:', attrId);
        } else {
            console.log('警告：检测到重复尝试创建字段，已跳过 - 属性ID:', attrId);
        }
    });
    
    // 6. 最终验证：检查表单中是否存在重复的selected_attributes值
    const finalHiddenInputs = form.querySelectorAll('input[name="selected_attributes"][type="hidden"]');
    const finalValues = Array.from(finalHiddenInputs).map(input => input.value);
    const uniqueFinalValues = [...new Set(finalValues)];
    
    if (finalValues.length !== uniqueFinalValues.length) {
        console.error('严重错误：表单中存在重复的属性值！');
        console.error('所有值:', finalValues.sort());
        console.error('唯一值:', uniqueFinalValues.sort());
        
        // 清理重复值，只保留第一个
        const valuesSeen = new Set();
        finalHiddenInputs.forEach(input => {
            if (valuesSeen.has(input.value)) {
                console.log('删除重复的隐藏字段:', input.value);
                input.remove();
            } else {
                valuesSeen.add(input.value);
            }
        });
    }
    
    console.log('*** 隐藏字段彻底更新完成 ***');
    console.log('最终全局状态:', globalSelectedAttributes.sort());
    console.log('表单中selected_attributes字段总数:', form.querySelectorAll('input[name="selected_attributes"]').length);
    console.log('其中隐藏字段数:', form.querySelectorAll('input[name="selected_attributes"][type="hidden"]').length);
    console.log('其中checkbox数:', form.querySelectorAll('input[name="selected_attributes"][type="checkbox"]').length);
    console.log('=== updateHiddenFields 执行完成 ===');
}

// 收集选中的属性并添加到表单
function collectSelectedAttributes() {
    console.log('=== collectSelectedAttributes 开始执行 ===');
    const form = document.querySelector('form');
    
    // 移除现有的selected_attributes隐藏字段
    const existingHiddenInputs = form.querySelectorAll('input[name="selected_attributes"][type="hidden"]');
    console.log('移除现有隐藏字段数量:', existingHiddenInputs.length);
    existingHiddenInputs.forEach(input => input.remove());
    
    // 获取动态渲染区域内的所有checkbox（不管是否选中）
    const attributesContent = document.getElementById('attributes-content');
    if (!attributesContent) {
        console.log('警告：未找到attributes-content元素');
        return;
    }
    
    const allCheckboxes = attributesContent.querySelectorAll('input[name="selected_attributes"]');
    const checkedBoxes = attributesContent.querySelectorAll('input[name="selected_attributes"]:checked');
    
    console.log('找到所有属性checkbox数量:', allCheckboxes.length);
    console.log('找到选中的checkbox数量:', checkedBoxes.length);
    
    // 输出所有checkbox的状态
    allCheckboxes.forEach(checkbox => {
        console.log(`属性 ${checkbox.value}: ${checkbox.checked ? '已选中' : '未选中'}`);
    });
    
    // 去重：收集唯一的属性ID
    const selectedAttrIds = new Set();
    checkedBoxes.forEach(checkbox => {
        // 确保是数字类型的ID
        const attrId = parseInt(checkbox.value);
        if (!isNaN(attrId)) {
            selectedAttrIds.add(attrId);
            console.log('收集到选中属性:', attrId);
        }
    });
    
    console.log('最终选中的唯一属性IDs:', Array.from(selectedAttrIds).sort((a,b) => a-b));
    
    // 为每个唯一的属性ID创建隐藏字段
    selectedAttrIds.forEach(attrId => {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'selected_attributes';
        hiddenInput.value = attrId;
        form.appendChild(hiddenInput);
        console.log('添加隐藏字段 - 属性ID:', attrId);
    });
    
    console.log('=== collectSelectedAttributes 执行完成，共添加', selectedAttrIds.size, '个隐藏字段 ===');
}

// 分类搜索功能
function initCategorySearch() {
    const categorySearch = document.getElementById('categorySearch');
    const categorySelect = document.getElementById('categorySelect');
    
    if (!categorySearch || !categorySelect) return;
    
    // 存储所有原始选项
    const allOptions = Array.from(categorySelect.options);
    
    categorySearch.addEventListener('input', function() {
        const searchText = this.value.toLowerCase().trim();
        
        // 清空当前选项
        categorySelect.innerHTML = '';
        
        if (searchText === '') {
            // 如果搜索框为空，显示所有选项
            allOptions.forEach(option => {
                categorySelect.appendChild(option.cloneNode(true));
            });
        } else {
            // 筛选匹配的选项
            allOptions.forEach(option => {
                const optionText = option.textContent.toLowerCase();
                if (optionText.includes(searchText)) {
                    categorySelect.appendChild(option.cloneNode(true));
                }
            });
        }
    });
    
    // 点击分类选择框时清空搜索
    categorySelect.addEventListener('change', function() {
        categorySearch.value = '';
    });
}

// 初始化分类搜索
document.addEventListener('DOMContentLoaded', function() {
    initCategorySearch();
});
</script>
{% endblock %}
