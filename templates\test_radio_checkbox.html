<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单选按钮和多选框测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>单选按钮和多选框测试</h1>
        
        <!-- 单选按钮测试 -->
        <div class="test-section">
            <h3>单选按钮测试 (材料选择)</h3>
            <div class="spec-options">
                <div class="spec-option radio-option">
                    <input type="radio" 
                           name="group_材料" 
                           id="attr_1" 
                           data-group="材料" 
                           data-id="1" 
                           data-is-multiple="false"
                           onchange="handleAttributeChange(this)">
                    <label for="attr_1" class="spec-option-label">木浆纸</label>
                </div>
                
                <div class="spec-option radio-option">
                    <input type="radio" 
                           name="group_材料" 
                           id="attr_2" 
                           data-group="材料" 
                           data-id="2" 
                           data-is-multiple="false"
                           onchange="handleAttributeChange(this)">
                    <label for="attr_2" class="spec-option-label">铜版纸</label>
                </div>
                
                <div class="spec-option radio-option">
                    <input type="radio" 
                           name="group_材料" 
                           id="attr_3" 
                           data-group="材料" 
                           data-id="3" 
                           data-is-multiple="false"
                           onchange="handleAttributeChange(this)">
                    <label for="attr_3" class="spec-option-label">哑粉纸</label>
                </div>
            </div>
        </div>
        
        <!-- 多选框测试 -->
        <div class="test-section">
            <h3>多选框测试 (工艺选择)</h3>
            <div class="spec-options">
                <div class="spec-option checkbox-option">
                    <input type="checkbox" 
                           id="attr_4" 
                           data-group="工艺" 
                           data-id="4" 
                           data-is-multiple="true"
                           data-max-selections="3"
                           onchange="handleAttributeChange(this)">
                    <label for="attr_4" class="spec-option-label">覆膜</label>
                </div>
                
                <div class="spec-option checkbox-option">
                    <input type="checkbox" 
                           id="attr_5" 
                           data-group="工艺" 
                           data-id="5" 
                           data-is-multiple="true"
                           data-max-selections="3"
                           onchange="handleAttributeChange(this)">
                    <label for="attr_5" class="spec-option-label">烫金</label>
                </div>
                
                <div class="spec-option checkbox-option">
                    <input type="checkbox" 
                           id="attr_6" 
                           data-group="工艺" 
                           data-id="6" 
                           data-is-multiple="true"
                           data-max-selections="3"
                           onchange="handleAttributeChange(this)">
                    <label for="attr_6" class="spec-option-label">UV</label>
                </div>
                
                <div class="spec-option checkbox-option">
                    <input type="checkbox" 
                           id="attr_7" 
                           data-group="工艺" 
                           data-id="7" 
                           data-is-multiple="true"
                           data-max-selections="3"
                           onchange="handleAttributeChange(this)">
                    <label for="attr_7" class="spec-option-label">压纹</label>
                </div>
            </div>
        </div>
        
        <!-- 调试信息 -->
        <div class="debug-info">
            <h5>调试信息</h5>
            <div id="debugInfo">点击选项查看选中状态...</div>
        </div>
        
        <!-- 提交测试 -->
        <div class="test-section">
            <button type="button" class="btn btn-primary" onclick="testSubmit()">测试提交数据</button>
            <div id="submitResult" class="mt-3"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedAttributes = [];
        
        // 属性变化处理函数（复制自产品详情页）
        function handleAttributeChange(element) {
            const group = element.dataset.group;
            const attrId = parseInt(element.dataset.id);
            const isMultiple = element.dataset.isMultiple === 'true';
            const maxSelections = parseInt(element.dataset.maxSelections) || null;

            if (!attrId) return;

            if (element.checked) {
                if (isMultiple) {
                    // 多选模式
                    // 检查是否超过最大选择数量
                    if (maxSelections) {
                        const currentGroupSelections = selectedAttributes.filter(id => {
                            const optionElement = document.querySelector(`input[data-id="${id}"]`);
                            return optionElement && optionElement.dataset.group === group;
                        });

                        if (currentGroupSelections.length >= maxSelections) {
                            element.checked = false;
                            alert(`${group}最多只能选择${maxSelections}个选项`);
                            return;
                        }
                    }

                    // 添加到选中列表
                    selectedAttributes.push(attrId);
                    console.log(`多选模式 - 添加属性: ${group} - ${attrId}`);
                    
                    // 为多选添加视觉反馈
                    element.closest('.spec-option').classList.add('selected');
                } else {
                    // 单选模式 - 清除同组的其他选择
                    document.querySelectorAll(`input[data-group="${group}"]`).forEach(input => {
                        if (input !== element && input.checked) {
                            input.checked = false;
                            // 从选中列表中移除
                            const inputAttrId = parseInt(input.dataset.id);
                            selectedAttributes = selectedAttributes.filter(id => id !== inputAttrId);
                            // 移除其他选项的视觉反馈
                            input.closest('.spec-option').classList.remove('selected');
                        }
                    });

                    // 添加当前选择
                    selectedAttributes.push(attrId);
                    console.log(`单选模式 - 选择属性: ${group} - ${attrId}`);
                    
                    // 为当前选项添加视觉反馈
                    element.closest('.spec-option').classList.add('selected');
                }
            } else {
                // 移除取消选中的属性
                selectedAttributes = selectedAttributes.filter(id => id !== attrId);
                console.log(`取消选择属性: ${group} - ${attrId}`);
                
                // 移除视觉反馈
                element.closest('.spec-option').classList.remove('selected');
            }

            console.log('Updated selected attributes:', selectedAttributes);
            updateDebugInfo();
        }
        
        // 更新调试信息
        function updateDebugInfo() {
            const debugDiv = document.getElementById('debugInfo');
            const groupedData = {};
            
            selectedAttributes.forEach(id => {
                const element = document.querySelector(`input[data-id="${id}"]`);
                if (element) {
                    const group = element.dataset.group;
                    const label = element.nextElementSibling.textContent;
                    if (!groupedData[group]) {
                        groupedData[group] = [];
                    }
                    groupedData[group].push({id, label});
                }
            });
            
            let html = `<strong>选中的属性 (${selectedAttributes.length}个):</strong><br>`;
            html += `原始数组: [${selectedAttributes.join(', ')}]<br><br>`;
            
            for (const [group, items] of Object.entries(groupedData)) {
                html += `<strong>${group}:</strong> `;
                html += items.map(item => `${item.label}(ID:${item.id})`).join(', ');
                html += '<br>';
            }
            
            if (selectedAttributes.length === 0) {
                html += '<em>暂无选中项</em>';
            }
            
            debugDiv.innerHTML = html;
        }
        
        // 测试提交
        function testSubmit() {
            const resultDiv = document.getElementById('submitResult');

            // 模拟后端处理逻辑
            const groupedData = {};
            selectedAttributes.forEach(id => {
                const element = document.querySelector(`input[data-id="${id}"]`);
                if (element) {
                    const group = element.dataset.group;
                    const label = element.nextElementSibling.textContent;
                    if (!groupedData[group]) {
                        groupedData[group] = [];
                    }
                    groupedData[group].push({id, label});
                }
            });

            const data = {
                selectedAttributes: selectedAttributes,
                groupedData: groupedData,
                timestamp: new Date().toISOString()
            };

            resultDiv.innerHTML = `
                <div class="alert alert-info">
                    <h6>提交的数据:</h6>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                    <hr>
                    <h6>模拟后端处理结果:</h6>
                    <ul>
                        ${Object.entries(groupedData).map(([group, items]) =>
                            `<li><strong>${group}:</strong> ${items.map(item => item.label).join(', ')}</li>`
                        ).join('')}
                    </ul>
                </div>
            `;
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo();
        });
    </script>
</body>
</html>
