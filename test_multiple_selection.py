#!/usr/bin/env python3
"""
测试多选功能的脚本
"""

from app import create_app
from models import db
from models.product import AttributeGroup, Attribute

def setup_multiple_selection_test():
    """设置多选测试数据"""
    app = create_app()
    with app.app_context():
        try:
            # 查找现有的属性组
            groups = AttributeGroup.query.all()
            print("现有属性组:")
            for group in groups:
                print(f"  - {group.name}: 多选={getattr(group, 'is_multiple', False)}, 最大选择={getattr(group, 'max_selections', None)}")
            
            # 找到工艺属性组并设置为多选，如果不存在则创建
            craft_group = AttributeGroup.query.filter_by(name='工艺').first()
            if not craft_group:
                # 创建工艺属性组
                craft_group = AttributeGroup(
                    name='工艺',
                    description='印刷工艺选择',
                    display_type='checkbox',
                    is_multiple=True,
                    max_selections=3,
                    category_id=1,  # 假设分类ID为1
                    sort_order=10
                )
                db.session.add(craft_group)
                db.session.flush()  # 获取ID

                # 创建工艺属性
                craft_attrs = [
                    ('覆膜', 5.0),
                    ('烫金', 8.0),
                    ('UV', 6.0),
                    ('压纹', 4.0),
                    ('凹凸', 7.0)
                ]

                for attr_name, price in craft_attrs:
                    attr = Attribute(
                        group_id=craft_group.id,
                        name='工艺',
                        value=attr_name,
                        price_modifier=price,
                        price_modifier_type='fixed',
                        sort_order=0
                    )
                    db.session.add(attr)

                print(f"\n✓ 创建 '{craft_group.name}' 属性组为多选，最多选择3个")
            else:
                craft_group.is_multiple = True
                craft_group.max_selections = 3
                print(f"\n✓ 设置 '{craft_group.name}' 为多选，最多选择3个")
            
            # 找到材质属性组保持单选
            material_group = AttributeGroup.query.filter_by(name='材质').first()
            if material_group:
                material_group.is_multiple = False
                material_group.max_selections = None
                print(f"✓ 设置 '{material_group.name}' 为单选")
            
            # 找到尺寸属性组保持单选
            size_group = AttributeGroup.query.filter_by(name='尺寸').first()
            if size_group:
                size_group.is_multiple = False
                size_group.max_selections = None
                print(f"✓ 设置 '{size_group.name}' 为单选")
            
            # 找到页数属性组保持单选
            pages_group = AttributeGroup.query.filter_by(name='页数').first()
            if pages_group:
                pages_group.is_multiple = False
                pages_group.max_selections = None
                print(f"✓ 设置 '{pages_group.name}' 为单选")
            
            db.session.commit()
            print("\n✅ 多选设置完成！")
            
            # 显示更新后的状态
            print("\n更新后的属性组状态:")
            groups = AttributeGroup.query.all()
            for group in groups:
                is_multiple = getattr(group, 'is_multiple', False)
                max_selections = getattr(group, 'max_selections', None)
                selection_type = "多选" if is_multiple else "单选"
                max_info = f"(最多{max_selections}个)" if max_selections else ""
                print(f"  - {group.name}: {selection_type} {max_info}")
                
                # 显示该组的属性
                attrs = group.attributes.all()
                for attr in attrs:
                    price_info = ""
                    if attr.price_modifier != 0:
                        if attr.price_modifier_type == 'percentage':
                            price_info = f" (+{attr.price_modifier}%)"
                        else:
                            price_info = f" (+¥{attr.price_modifier})"
                    print(f"    * {attr.value}{price_info}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 设置失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    setup_multiple_selection_test()
